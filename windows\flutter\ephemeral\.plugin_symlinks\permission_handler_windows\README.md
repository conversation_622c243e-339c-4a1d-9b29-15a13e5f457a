# permission_handler_windows

[![pub package](https://img.shields.io/pub/v/permission_handler_windows.svg)](https://pub.dartlang.org/packages/permission_handler_windows) ![Build status](https://github.com/Baseflow/flutter-permission-handler/workflows/permission_handler_android/badge.svg?branch=master) [![style: flutter lints](https://img.shields.io/badge/style-flutter_lints-40c4ff.svg)](https://pub.dev/packages/flutter_lints)

The official Windows implementation of the [permission_handler](https://pub.dev/packages/permission_handler) plugin by [Baseflow](https://baseflow.com).

## Usage

Starting from version 9.2.0 of the [permission_handler](https://pub.dev/packages/permission_handler) plugin this is the endorsed Windows implementation. This means it will automatically be added to your dependencies when you depend on `permission_handler: ^9.2.0` in your applications pubspec.yaml.

More detailed instructions on using the API can be found in the [README.md](../permission_handler/README.md) of the [permission_handler](https://pub.dev/packages/permission_handler) package.

## Issues

Please file any issues, bugs or feature requests as an issue on our [GitHub](https://github.com/Baseflow/flutter-permission-handler/issues) page. Commercial support is available, you can contact us at <<EMAIL>>.

## Want to contribute

If you would like to contribute to the plugin (e.g. by improving the documentation, solving a bug or adding a cool new feature), please carefully review our [contribution guide](../CONTRIBUTING.md) and send us your [pull request](https://github.com/Baseflow/flutter-permission-handler/pulls).

## Author

This permission_handler plugin for Flutter is developed by [Baseflow](https://baseflow.com).

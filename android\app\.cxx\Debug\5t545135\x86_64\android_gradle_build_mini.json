{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ard2.0\\arduinocontroller\\android\\app\\.cxx\\Debug\\5t545135\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\ard2.0\\arduinocontroller\\android\\app\\.cxx\\Debug\\5t545135\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
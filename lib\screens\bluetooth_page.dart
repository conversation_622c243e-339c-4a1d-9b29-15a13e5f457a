import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/bluetooth_service.dart';
import '../widgets/movement_panel.dart';
import '../widgets/action_panel.dart';

class BluetoothPage extends StatefulWidget {
  final String? macAddress;

  const BluetoothPage({
    Key? key,
    this.macAddress,
  }) : super(key: key);

  @override
  State<BluetoothPage> createState() => _BluetoothPageState();
}

class _BluetoothPageState extends State<BluetoothPage> with WidgetsBindingObserver {
  final BluetoothService _bluetoothService = BluetoothService();
  bool _isHoldingButton = false;
  bool _isConnecting = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive ||
        state == AppLifecycleState.detached) {
      _bluetoothService.disconnect();
    }
  }

  Future<void> _initBluetoothConnection() async {
    // Prevent multiple simultaneous connection attempts
    if (_isConnecting) return;

    setState(() {
      _isConnecting = true;
    });

    try {
      bool permissionsGranted = await _bluetoothService.checkPermissions();
      if (!permissionsGranted) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Permissões Bluetooth necessárias não foram concedidas.'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Se tiver um MAC específico, salva ele antes de tentar conectar
      if (widget.macAddress != null) {
        await _bluetoothService.saveMacAddress(widget.macAddress!);
      }

      // Attempt connection with improved logic (no manual retries needed)
      bool connected = await _bluetoothService.connectToDevice();

      if (!connected && mounted) {
        // Show error message if connection failed
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Não foi possível conectar ao dispositivo. Verifique se está ligado e no alcance.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 4),
          ),
        );
      } else if (connected && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Conectado com sucesso!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isConnecting = false;
        });
      }
    }
  }

  void _handleButtonPress(String command) {
    if (!_isHoldingButton) {
      _bluetoothService.sendCommand(command);
    }
  }

  void _startContinuousCommand(String command) {
    setState(() {
      _isHoldingButton = true;
    });
    _sendContinuousCommand(command);
  }

  void _stopContinuousCommand() {
    setState(() {
      _isHoldingButton = false;
    });
  }

  Future<void> _sendContinuousCommand(String command) async {
    while (_isHoldingButton) {
      await _bluetoothService.sendCommand(command);
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Controle do Carrinho'),
        actions: [
          // Botão de reconectar na AppBar antes do status
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: ElevatedButton.icon(
              onPressed: _isConnecting ? null : () async {
                // Se tiver um MAC específico, salva ele antes de tentar conectar
                if (widget.macAddress != null) {
                  await _bluetoothService.saveMacAddress(widget.macAddress!);
                }
                await _initBluetoothConnection();
              },
              icon: _isConnecting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.bluetooth_searching, size: 20),
              label: Text(_isConnecting ? 'Conectando...' : 'Conectar'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _isConnecting ? Colors.grey : Colors.blue[700],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                textStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: ValueListenableBuilder<String>(
                valueListenable: _bluetoothService.connectionStatus,
                builder: (context, status, child) {
                  return Text(
                    'Status: $status',
                    style: const TextStyle(fontSize: 16),
                  );
                },
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.bluetooth_disabled),
            onPressed: _bluetoothService.disconnect,
            tooltip: 'Desconectar',
          ),
        ],
      ),
      body: Row(
        children: [
          // Controles de movimento (lado esquerdo)
          Expanded(
            child: MovementPanel(
              onStartContinuousCommand: _startContinuousCommand,
              onStopContinuousCommand: _stopContinuousCommand,
              onButtonPress: _handleButtonPress,
            ),
          ),

          // Botões de ação (lado direito)
          Expanded(
            child: ActionPanel(
              onCommandSend: _handleButtonPress,
              onReconnect: () {}
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _bluetoothService.disconnect();
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    super.dispose();
  }
}
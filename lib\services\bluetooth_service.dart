import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import 'package:permission_handler/permission_handler.dart';
import 'storage_service.dart';

class BluetoothService {
  static final BluetoothService _instance = BluetoothService._internal();

  factory BluetoothService() => _instance;

  BluetoothService._internal();

  final StorageService _storageService = StorageService();
  fbp.BluetoothDevice? _connectedDevice;
  fbp.BluetoothCharacteristic? _writeCharacteristic;

  // Controle do estado
  final ValueNotifier<String> connectionStatus = ValueNotifier<String>('Desconectado');
  final ValueNotifier<List<fbp.ScanResult>> scanResults = ValueNotifier<List<fbp.ScanResult>>([]);
  final ValueNotifier<bool> isScanning = ValueNotifier<bool>(false);

  bool get isConnected => _connectedDevice != null && _writeCharacteristic != null;

  Future<bool> checkPermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.location,
    ].request();

    bool allGranted = true;
    statuses.forEach((permission, status) {
      if (!status.isGranted) {
        allGranted = false;
      }
    });

    return allGranted;
  }

  Future<bool> connectToDevice() async {
    final targetMac = await _storageService.getMacAddress();
    if (targetMac == null) {
      connectionStatus.value = 'Endereço MAC não configurado';
      return false;
    }

    // Check if already connected to the target device
    if (_connectedDevice != null &&
        _connectedDevice!.remoteId.toString().toUpperCase() == targetMac.toUpperCase() &&
        _writeCharacteristic != null) {
      try {
        // Verify the connection is still active
        await _connectedDevice!.readRssi();
        connectionStatus.value = 'Conectado';
        return true;
      } catch (e) {
        // Connection is stale, clean it up
        await _cleanupConnection();
      }
    }

    // Clean up any existing connection before attempting new one
    await _cleanupConnection();

    connectionStatus.value = 'Conectando...';

    try {
      // Ensure any previous scan is stopped
      if (fbp.FlutterBluePlus.isScanningNow) {
        await fbp.FlutterBluePlus.stopScan();
        // Wait a bit for the scan to fully stop
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Start scanning with longer timeout for better reliability
      await fbp.FlutterBluePlus.startScan(timeout: const Duration(seconds: 8));

      bool deviceFound = false;
      fbp.BluetoothDevice? targetDevice;

      // Listen to scan results with timeout
      final scanCompleter = Completer<bool>();
      late StreamSubscription scanSubscription;

      scanSubscription = fbp.FlutterBluePlus.scanResults.listen((results) {
        for (fbp.ScanResult r in results) {
          if (r.device.remoteId.toString().toUpperCase() == targetMac.toUpperCase()) {
            targetDevice = r.device;
            deviceFound = true;
            scanCompleter.complete(true);
            break;
          }
        }
      });

      // Wait for device to be found or timeout
      try {
        await scanCompleter.future.timeout(const Duration(seconds: 10));
      } catch (e) {
        // Timeout or error occurred
      } finally {
        await scanSubscription.cancel();
        await fbp.FlutterBluePlus.stopScan();
      }

      if (!deviceFound || targetDevice == null) {
        connectionStatus.value = 'Dispositivo não encontrado';
        return false;
      }

      connectionStatus.value = 'Dispositivo encontrado, conectando...';

      // Connect to the device with timeout
      await targetDevice!.connect(timeout: const Duration(seconds: 10));

      connectionStatus.value = 'Descobrindo serviços...';

      // Discover services with timeout
      List<fbp.BluetoothService> services = await targetDevice!.discoverServices()
          .timeout(const Duration(seconds: 10));

      // Find write characteristic
      fbp.BluetoothCharacteristic? writeChar;
      for (fbp.BluetoothService service in services) {
        for (fbp.BluetoothCharacteristic characteristic in service.characteristics) {
          if (characteristic.properties.write) {
            writeChar = characteristic;
            break;
          }
        }
        if (writeChar != null) break;
      }

      if (writeChar == null) {
        await targetDevice!.disconnect();
        connectionStatus.value = 'Característica de escrita não encontrada';
        return false;
      }

      // Set up connection state
      _connectedDevice = targetDevice;
      _writeCharacteristic = writeChar;

      // Verify connection is working by testing RSSI
      try {
        await _connectedDevice!.readRssi();
        connectionStatus.value = 'Conectado';
        return true;
      } catch (e) {
        await _cleanupConnection();
        connectionStatus.value = 'Falha na verificação da conexão';
        return false;
      }

    } catch (e) {
      await _cleanupConnection();
      connectionStatus.value = 'Erro na conexão: ${e.toString()}';
      return false;
    }
  }

  // Helper method to clean up connection state
  Future<void> _cleanupConnection() async {
    try {
      if (_connectedDevice != null) {
        await _connectedDevice!.disconnect();
      }
    } catch (e) {
      // Ignore disconnect errors during cleanup
    } finally {
      _connectedDevice = null;
      _writeCharacteristic = null;
    }
  }

  Future<void> disconnect() async {
    try {
      await _cleanupConnection();
      connectionStatus.value = 'Desconectado';
    } catch (e) {
      connectionStatus.value = 'Erro ao desconectar';
    }
  }

  Future<void> saveMacAddress(String macAddress) async {
    await _storageService.saveMacAddress(macAddress);
  }

  Future<bool> sendCommand(String command) async {
    if (_writeCharacteristic != null) {
      try {
        await _writeCharacteristic!.write(command.codeUnits);
        return true;
      } catch (e) {
        connectionStatus.value = 'Erro ao enviar comando';
        return false;
      }
    } else {
      connectionStatus.value = 'Desconectado';
      return false;
    }
  }

  // Método para escanear dispositivos BLE
  Future<void> startScan({Duration timeout = const Duration(seconds: 10)}) async {
    try {
      // Limpar resultados anteriores
      scanResults.value = [];
      isScanning.value = true;
      connectionStatus.value = 'Escaneando dispositivos...';

      // Iniciar escaneamento
      await fbp.FlutterBluePlus.startScan(timeout: timeout);

      // Escutar resultados do escaneamento
      fbp.FlutterBluePlus.scanResults.listen((results) {
        List<fbp.ScanResult> uniqueResults = [];

        // Filtrar resultados duplicados
        for (fbp.ScanResult result in results) {
          if (!uniqueResults.any((r) => r.device.remoteId == result.device.remoteId)) {
            uniqueResults.add(result);
          }
        }

        scanResults.value = uniqueResults;
      });

      // Escutar estado do escaneamento
      fbp.FlutterBluePlus.isScanning.listen((scanning) {
        isScanning.value = scanning;
        if (!scanning) {
          connectionStatus.value = 'Escaneamento concluído. ${scanResults.value.length} dispositivos encontrados.';
        }
      });
    } catch (e) {
      isScanning.value = false;
      connectionStatus.value = 'Erro ao escanear: $e';
    }
  }

  Future<void> stopScan() async {
    try {
      await fbp.FlutterBluePlus.stopScan();
    } catch (e) {
      connectionStatus.value = 'Erro ao parar escaneamento: $e';
    }
  }

  // Método para conectar a um dispositivo específico a partir do resultado do escaneamento
  Future<bool> connectToScannedDevice(fbp.ScanResult result) async {
    try {
      connectionStatus.value = 'Conectando a ${result.device.remoteId}...';

      // Clean up any existing connection
      await _cleanupConnection();

      // Parar escaneamento antes de conectar
      await stopScan();

      // Conectar ao dispositivo com timeout
      await result.device.connect(timeout: const Duration(seconds: 10));

      connectionStatus.value = 'Descobrindo serviços...';

      // Descobrir serviços e características com timeout
      List<fbp.BluetoothService> services = await result.device.discoverServices()
          .timeout(const Duration(seconds: 10));

      // Find write characteristic
      fbp.BluetoothCharacteristic? writeChar;
      for (fbp.BluetoothService service in services) {
        for (fbp.BluetoothCharacteristic characteristic in service.characteristics) {
          if (characteristic.properties.write) {
            writeChar = characteristic;
            break;
          }
        }
        if (writeChar != null) break;
      }

      if (writeChar == null) {
        await result.device.disconnect();
        connectionStatus.value = 'Característica de escrita não encontrada';
        return false;
      }

      // Set up connection state
      _connectedDevice = result.device;
      _writeCharacteristic = writeChar;

      // Verify connection is working
      try {
        await _connectedDevice!.readRssi();
        connectionStatus.value = 'Conectado a ${result.device.remoteId}';

        // Salvar o endereço MAC para conexões futuras
        await _storageService.saveMacAddress(result.device.remoteId.toString());

        return true;
      } catch (e) {
        await _cleanupConnection();
        connectionStatus.value = 'Falha na verificação da conexão';
        return false;
      }

    } catch (e) {
      await _cleanupConnection();
      connectionStatus.value = 'Erro ao conectar: $e';
      return false;
    }
  }
}